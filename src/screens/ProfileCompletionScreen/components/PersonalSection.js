import React, { useState } from "react";
import { Image, TouchableOpacity, View } from "react-native";
import PropTypes from "prop-types";
import InputField from "../../../components/InputField";
import PhoneNumberField from "../../../components/PhoneNumberField";
import MyText from "../../../components/MyText";
import CollapsibleHeader from "./CollapsibleHeader";
import NationalitySelector from "../../../components/NationalitySelector";
import SelectField from "../../../components/SelectField";
import DatePicker from "../../../components/DatePicker";
import { styles } from "../styles";
import { maritalStatusOptions } from "../../../utils/constants";
import { timezones } from "../../../utils/timezones";
import icons from "../../../assets/icons";
import commonStyles from "../../../assets/commonStyles";
import colors from "../../../assets/colors";

const genderOptions = [
  { label: "Male", value: "male" },
  { label: "Female", value: "female" },
  { label: "Other", value: "other" },
];

const PersonalSection = React.forwardRef(
  (
    {
      form,
      handleChange,
      errors,
      phoneInputRef,
      onTimezoneDropdownToggle,
      setErrors,
      ownProfileData,
      additionalEmails = [],
      setAdditionalEmails = () => {},
      additionalPhones = [],
      setAdditionalPhones = () => {},
    },
    ref
  ) => {
    const [isBasicCollapsed, setIsBasicCollapsed] = useState(false);
    const [isInsuranceCollapsed, setIsInsuranceCollapsed] = useState(true);
    const [isAddressCollapsed, setIsAddressCollapsed] = useState(true);
    const [isOtherAddressCollapsed, setIsOtherAddressCollapsed] =
      useState(true);
    const [isBillingCollapsed, setIsBillingCollapsed] = useState(true);
    const [isCardCollapsed, setIsCardCollapsed] = useState(true);
    const [isAccountCollapsed, setIsAccountCollapsed] = useState(true);
    const [isEmergencyCollapsed, setIsEmergencyCollapsed] = useState(true);
    const [countryPickerVisible, setCountryPickerVisible] = useState(false);
    const [isOtherDetailsCollapsed, setIsOtherDetailsCollapsed] =
      useState(false);

    // Visibility states for fields
    const [fieldVisibility, setFieldVisibility] = useState({
      // Basic Details
      firstName: true,
      middleName: true,
      lastName: true,
      nickname: true,
      dateOfBirth: true,
      gender: true,
      nationality: true,
      maritalStatus: true,
      spouseName: true,

      // Home Address
      apartment: true,
      street: true,
      city: true,
      state: true,
      zipCode: true,
      country: true,

      // Other Address
      otherApartment: true,
      otherStreet: true,
      otherCity: true,
      otherState: true,
      otherZipCode: true,
      otherCountry: true,

      // Other Details
      secondaryEmail: true,
      secondaryPhone: true,
      personalWebsite: true,
      hobbies: true,
      religion: true,
      preferredContactMethod: true,
      timezone: true,

      // Emergency Contact
      emergencyContactName: true,
      emergencyContactRelationship: true,
      emergencyEmail: true,
      emergencyPhone: true,
      emergencyAddress: true,

      // Health Insurance
      policyNumber: true,
      insuranceProvider: true,
      policyPeriod: true,
      effectiveDate: true,
      expirationDate: true,
      sumInsured: true,

      // Billing Address
      billingApartment: true,
      billingStreet: true,
      billingCity: true,
      billingState: true,
      billingZipCode: true,
      billingCountry: true,

      // Card Details
      cardName: true,
      cardNumber: true,
      cardExpiry: true,
      cardCvv: true,

      // Bank Account
      accountName: true,
      bankName: true,
      accountNumber: true,
      ifscCode: true,
    });

    // Handle visibility toggle for a field
    const handleVisibilityToggle = (fieldName) => {
      setFieldVisibility((prev) => ({
        ...prev,
        [fieldName]: !prev[fieldName],
      }));
    };

    // Expose methods through the ref
    React.useImperativeHandle(ref, () => ({
      getFieldVisibility: () => fieldVisibility,
    }));

    // Notify parent component when timezone dropdown state changes
    const handleTimezoneDropdownToggle = (isOpen) => {
      if (onTimezoneDropdownToggle) {
        onTimezoneDropdownToggle(isOpen);
      }
    };

    // Validate card expiry date (MM/YY format)
    const validateCardExpiry = (expiry) => {
      if (!expiry || expiry.length < 5)
        return "Please enter a valid expiry date";

      const parts = expiry.split("/");
      if (parts.length !== 2) return "Invalid format";

      const month = parseInt(parts[0], 10);
      const year = parseInt("20" + parts[1], 10);

      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1;

      if (month < 1 || month > 12) return "Invalid month";
      if (year < currentYear) return "Card expired";
      if (year === currentYear && month < currentMonth) return "Card expired";

      return "";
    };

    // Validate card number using Luhn algorithm (basic check)
    const validateCardNumber = (cardNumber) => {
      const cleaned = cardNumber.replace(/\s/g, "");
      if (cleaned.length < 13 || cleaned.length > 19)
        return "Invalid card number length";

      // Basic format check
      if (!/^\d+$/.test(cleaned))
        return "Card number should contain only digits";

      return "";
    };

    return (
      <View style={{ flex: 1 }}>
        <CollapsibleHeader
          title="Basic Details"
          isCollapsed={isBasicCollapsed}
          onToggle={() => setIsBasicCollapsed(!isBasicCollapsed)}
        />

        {!isBasicCollapsed && (
          <View>
            <InputField
              label="First Name*"
              value={form.firstName}
              onChangeText={(val) => handleChange("firstName", val)}
              error={errors.firstName}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.firstName}
              disabled={true}
              onVisibilityToggle={() => handleVisibilityToggle("firstName")}
            />
            <InputField
              label="Middle Name"
              value={form.middleName}
              onChangeText={(val) => handleChange("middleName", val)}
              error={errors.middleName}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.middleName}
              disabled={true}
              onVisibilityToggle={() => handleVisibilityToggle("middleName")}
            />

            <InputField
              label="Last Name*"
              value={form.lastName}
              onChangeText={(val) => handleChange("lastName", val)}
              error={errors.lastName}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.lastName}
              disabled={true}
              onVisibilityToggle={() => handleVisibilityToggle("lastName")}
            />
            <InputField
              label="Nickname*"
              value={form.nickname}
              onChangeText={(val) => handleChange("nickname", val)}
              error={errors.nickname}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.nickname}
              onVisibilityToggle={() => handleVisibilityToggle("nickname")}
            />

            <InputField
              label="Email*"
              value={form.email}
              onChangeText={(val) => handleChange("email", val)}
              error={errors.email}
              keyboardType="email-address"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.email}
              disabled={true}
              onVisibilityToggle={() => handleVisibilityToggle("email")}
            />

            {additionalEmails.map((email, idx) => (
              <View key={`additional-email-${idx}`} style={{}}>
                {/* Custom label with cross button */}
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    // justifyContent: "space-between",
                    marginBottom: 6,
                  }}
                >
                  <MyText p medium style={{ color: "#000" }}>
                    {`Additional Email ${idx + 1}`}
                  </MyText>
                  <TouchableOpacity
                    onPress={() => {
                      console.log("close icon pressed");
                      const updated = additionalEmails.filter(
                        (_, i) => i !== idx
                      );
                      setAdditionalEmails(updated);
                    }}
                    style={{ padding: 4 }}
                  >
                    <Image
                      source={icons.closeIcon}
                      resizeMode="contain"
                      style={commonStyles.smallIcon}
                    />
                  </TouchableOpacity>
                </View>

                {/* Input field without label since we have custom label above */}
                <InputField
                  placeholder={`Add additional email ${idx + 1}`}
                  value={email}
                  onChangeText={(val) => {
                    const updated = [...additionalEmails];
                    updated[idx] = val;
                    setAdditionalEmails(updated);
                  }}
                  keyboardType="email-address"
                  showVisibilityToggle={true}
                  isVisible={true}
                  onVisibilityToggle={() => {}}
                  error={errors[`additionalEmail${idx}`]}
                />
              </View>
            ))}

            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() => setAdditionalEmails([...additionalEmails, ""])}
            >
              + Add more email
            </MyText>

            <InputField
              label="Phone No.*"
              value={
                form.callingCode
                  ? `+${form.callingCode}-${form.phone}`
                  : form.phone
              }
              onChangeText={(val) => handleChange("phone", val)}
              error={errors.phone}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.phone}
              disabled={true}
              onVisibilityToggle={() => handleVisibilityToggle("phone")}
            />

            {additionalPhones.map((phone, idx) => (
              <PhoneNumberField
                key={`additional-phone-${idx}`}
                label={`Additional Phone ${idx + 1}`}
                labelP={true}
                labelMedium={true}
                labelStyle={{}}
                value={phone}
                phoneInputRef={phoneInputRef}
                onChangeRaw={(val) => {
                  const updated = [...additionalPhones];
                  updated[idx] = val;
                  setAdditionalPhones(updated);
                }}
                setCountryCode={() => {}}
                error={errors[`additionalPhone${idx}`]}
                setError={() => {}}
                showVisibilityToggle={true}
                isVisible={true}
                onVisibilityToggle={() => {}}
                rightIcon="close"
                onRightIconPress={() => {
                  const updated = additionalPhones.filter((_, i) => i !== idx);
                  setAdditionalPhones(updated);
                }}
              />
            ))}

            <MyText
            und
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() => setAdditionalPhones([...additionalPhones, ""])}
            >
              + Add more contact
            </MyText>

            <DatePicker
              label="Date of Birth*"
              value={form.dateOfBirth}
              onChangeDate={(date) =>
                handleChange("dateOfBirth", date.toISOString().split("T")[0])
              }
              error={errors.dateOfBirth}
              placeholder="Select date of birth"
              maximumDate={new Date()}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.dateOfBirth}
              onVisibilityToggle={() => handleVisibilityToggle("dateOfBirth")}
            />
            <SelectField
              label="Gender"
              data={genderOptions}
              defaultValue={form.gender}
              onSelect={(item) => handleChange("gender", item.value)}
              backgroundColor="#f2f2f2"
              height={48}
              width="100%"
              showIcon={true}
              error={errors.gender}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.gender}
              onVisibilityToggle={() => handleVisibilityToggle("gender")}
            />
            <NationalitySelector
              value={form.nationality}
              onSelect={(val) => handleChange("nationality", val)}
              error={errors.nationality}
              isPickerVisible={countryPickerVisible}
              setPickerVisible={setCountryPickerVisible}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.nationality}
              onVisibilityToggle={() => handleVisibilityToggle("nationality")}
            />
            <SelectField
              label="Marital Status"
              data={maritalStatusOptions}
              defaultValue={form.maritalStatus}
              onSelect={(item) => handleChange("maritalStatus", item.value)}
              backgroundColor="#f2f2f2"
              height={48}
              width="100%"
              showIcon={true}
              error={errors.maritalStatus}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.maritalStatus}
              onVisibilityToggle={() => handleVisibilityToggle("maritalStatus")}
            />
            {form.maritalStatus == "Married" && (
              <InputField
                label="Spouse Name"
                value={form.spouseName}
                onChangeText={(val) => handleChange("spouseName", val)}
                error={errors.spouseName}
                showVisibilityToggle={true}
                isVisible={fieldVisibility.spouseName}
                onVisibilityToggle={() => handleVisibilityToggle("spouseName")}
              />
            )}
          </View>
        )}

        <CollapsibleHeader
          title="Home Address"
          isCollapsed={isAddressCollapsed}
          onToggle={() => setIsAddressCollapsed(!isAddressCollapsed)}
        />
        {!isAddressCollapsed && (
          <View style={{ zIndex: 1, position: "relative" }}>
            <InputField
              label="Apartment/House No./Building"
              value={form.apartment}
              onChangeText={(val) => handleChange("apartment", val)}
              error={errors.apartment}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.apartment}
              onVisibilityToggle={() => handleVisibilityToggle("apartment")}
            />
            <InputField
              label="Street"
              value={form.street}
              onChangeText={(val) => handleChange("street", val)}
              error={errors.street}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.street}
              onVisibilityToggle={() => handleVisibilityToggle("street")}
            />
            <InputField
              label="City"
              value={form.city}
              onChangeText={(val) => handleChange("city", val)}
              error={errors.city}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.city}
              onVisibilityToggle={() => handleVisibilityToggle("city")}
            />
            <InputField
              label="State/Province"
              value={form.state}
              onChangeText={(val) => handleChange("state", val)}
              error={errors.state}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.state}
              onVisibilityToggle={() => handleVisibilityToggle("state")}
            />
            <InputField
              label="ZIP/Postal Code"
              value={form.zipCode}
              onChangeText={(val) => handleChange("zipCode", val)}
              error={errors.zipCode}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.zipCode}
              onVisibilityToggle={() => handleVisibilityToggle("zipCode")}
            />
            <InputField
              label="Country"
              value={form.country}
              onChangeText={(val) => handleChange("country", val)}
              error={errors.country}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.country}
              onVisibilityToggle={() => handleVisibilityToggle("country")}
            />
          </View>
        )}

        <CollapsibleHeader
          title="Other Address"
          isCollapsed={isOtherAddressCollapsed}
          onToggle={() => setIsOtherAddressCollapsed(!isOtherAddressCollapsed)}
          showCheckbox
          checkboxText="Same as Home"
          isChecked={form.isSameAddress}
          onCheckboxToggle={() => {
            const newValue = !form.isSameAddress;
            handleChange("isSameAddress", newValue);

            if (newValue) {
              handleChange("otherApartment", form.apartment);
              handleChange("otherStreet", form.street);
              handleChange("otherCity", form.city);
              handleChange("otherState", form.state);
              handleChange("otherZipCode", form.zipCode);
              handleChange("otherCountry", form.country);
            }
          }}
        />
        {!isOtherAddressCollapsed && (
          <View style={{ zIndex: 1, position: "relative" }}>
            <InputField
              label="Apartment/House No./Building"
              value={form.isSameAddress ? form.apartment : form.otherApartment}
              onChangeText={(val) => handleChange("otherApartment", val)}
              error={errors.otherApartment}
              disabled={form.isSameAddress}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.otherApartment}
              onVisibilityToggle={() =>
                handleVisibilityToggle("otherApartment")
              }
            />
            <InputField
              label="Street"
              value={form.isSameAddress ? form.street : form.otherStreet}
              onChangeText={(val) => handleChange("otherStreet", val)}
              error={errors.otherStreet}
              disabled={form.isSameAddress}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.otherStreet}
              onVisibilityToggle={() => handleVisibilityToggle("otherStreet")}
            />
            <InputField
              label="City"
              value={form.isSameAddress ? form.city : form.otherCity}
              onChangeText={(val) => handleChange("otherCity", val)}
              error={errors.otherCity}
              disabled={form.isSameAddress}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.otherCity}
              onVisibilityToggle={() => handleVisibilityToggle("otherCity")}
            />
            <InputField
              label="State/Province"
              value={form.isSameAddress ? form.state : form.otherState}
              onChangeText={(val) => handleChange("otherState", val)}
              error={errors.otherState}
              disabled={form.isSameAddress}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.otherState}
              onVisibilityToggle={() => handleVisibilityToggle("otherState")}
            />
            <InputField
              label="ZIP/Postal Code"
              value={form.isSameAddress ? form.zipCode : form.otherZipCode}
              onChangeText={(val) => handleChange("otherZipCode", val)}
              error={errors.otherZipCode}
              disabled={form.isSameAddress}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.otherZipCode}
              onVisibilityToggle={() => handleVisibilityToggle("otherZipCode")}
            />
            <InputField
              label="Country"
              value={form.isSameAddress ? form.country : form.otherCountry}
              onChangeText={(val) => handleChange("otherCountry", val)}
              error={errors.otherCountry}
              disabled={form.isSameAddress}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.otherCountry}
              onVisibilityToggle={() => handleVisibilityToggle("otherCountry")}
            />
          </View>
        )}
        <CollapsibleHeader
          title="Other Details"
          isCollapsed={isOtherDetailsCollapsed}
          onToggle={() => setIsOtherDetailsCollapsed(!isOtherDetailsCollapsed)}
        />
        {!isOtherDetailsCollapsed && (
          <View style={{ zIndex: 1, position: "relative" }}>
            <InputField
              label="Secondary Email"
              value={form.secondaryEmail}
              onChangeText={(val) => handleChange("secondaryEmail", val)}
              error={errors.secondaryEmail}
              keyboardType="email-address"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.secondaryEmail}
              onVisibilityToggle={() =>
                handleVisibilityToggle("secondaryEmail")
              }
            />
            <PhoneNumberField
              label="Secondary Phone No."
              labelP={true}
              labelMedium={true}
              value={form.secondaryPhone}
              phoneInputRef={phoneInputRef}
              onChangeRaw={(val) => handleChange("secondaryPhone", val)}
              setCountryCode={() => {}}
              error={errors.secondaryPhone}
              setError={() => {}}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.secondaryPhone}
              onVisibilityToggle={() =>
                handleVisibilityToggle("secondaryPhone")
              }
            />
            <InputField
              label="Personal Website (if any)"
              value={form.personalWebsite}
              onChangeText={(val) => handleChange("personalWebsite", val)}
              error={errors.personalWebsite}
              placeholder="https://example.com"
              keyboardType="url"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.personalWebsite}
              onVisibilityToggle={() =>
                handleVisibilityToggle("personalWebsite")
              }
            />
            <InputField
              label="Hobbies/Interests"
              value={form.hobbies}
              onChangeText={(val) => handleChange("hobbies", val)}
              error={errors.hobbies}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.hobbies}
              onVisibilityToggle={() => handleVisibilityToggle("hobbies")}
            />
            <InputField
              label="Religion"
              value={form.religion}
              onChangeText={(val) => handleChange("religion", val)}
              error={errors.religion}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.religion}
              onVisibilityToggle={() => handleVisibilityToggle("religion")}
            />
            <InputField
              label="Preferred Contact Method"
              value={form.preferredContactMethod}
              onChangeText={(val) =>
                handleChange("preferredContactMethod", val)
              }
              error={errors.preferredContactMethod}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.preferredContactMethod}
              onVisibilityToggle={() =>
                handleVisibilityToggle("preferredContactMethod")
              }
            />
            <SelectField
              label="Timezone"
              data={timezones}
              defaultValue={form.timezone}
              onSelect={(item) => handleChange("timezone", item.value)}
              backgroundColor="#f2f2f2"
              height={48}
              width="100%"
              showIcon={true}
              error={errors.timezone}
              onDropdownToggle={handleTimezoneDropdownToggle}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.timezone}
              onVisibilityToggle={() => handleVisibilityToggle("timezone")}
            />
          </View>
        )}
        <CollapsibleHeader
          title="Emergency Contact Details"
          isCollapsed={isEmergencyCollapsed}
          onToggle={() => setIsEmergencyCollapsed(!isEmergencyCollapsed)}
        />
        {!isEmergencyCollapsed && (
          <View style={{ zIndex: 1, position: "relative" }}>
            <InputField
              label="Emergency Contact Name"
              value={form.emergencyContactName}
              onChangeText={(val) => handleChange("emergencyContactName", val)}
              error={errors.emergencyContactName}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.emergencyContactName}
              onVisibilityToggle={() =>
                handleVisibilityToggle("emergencyContactName")
              }
            />
            <InputField
              label="Emergency Contact Relationship"
              value={form.emergencyContactRelationship}
              onChangeText={(val) =>
                handleChange("emergencyContactRelationship", val)
              }
              error={errors.emergencyContactRelationship}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.emergencyContactRelationship}
              onVisibilityToggle={() =>
                handleVisibilityToggle("emergencyContactRelationship")
              }
            />
            <InputField
              label="Emergency Email"
              value={form.emergencyEmail}
              onChangeText={(val) => handleChange("emergencyEmail", val)}
              error={errors.emergencyEmail}
              keyboardType="email-address"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.emergencyEmail}
              onVisibilityToggle={() =>
                handleVisibilityToggle("emergencyEmail")
              }
            />
            <PhoneNumberField
              label="Emergency Phone No."
              labelP={true}
              labelMedium={true}
              value={form.emergencyPhone}
              phoneInputRef={phoneInputRef}
              onChangeRaw={(val) => handleChange("emergencyPhone", val)}
              setCountryCode={() => {}}
              error={errors.emergencyPhone}
              setError={() => {}}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.emergencyPhone}
              onVisibilityToggle={() =>
                handleVisibilityToggle("emergencyPhone")
              }
            />
            <InputField
              label="Emergency Address"
              value={form.emergencyAddress}
              onChangeText={(val) => handleChange("emergencyAddress", val)}
              error={errors.emergencyAddress}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.emergencyAddress}
              onVisibilityToggle={() =>
                handleVisibilityToggle("emergencyAddress")
              }
            />
          </View>
        )}

        <CollapsibleHeader
          title="Health Insurance Information"
          isCollapsed={isInsuranceCollapsed}
          onToggle={() => setIsInsuranceCollapsed(!isInsuranceCollapsed)}
        />
        {!isInsuranceCollapsed && (
          <View style={{ zIndex: 1, position: "relative" }}>
            <InputField
              label="Policy Number"
              value={form.policyNumber}
              onChangeText={(val) => handleChange("policyNumber", val)}
              error={errors.policyNumber}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.policyNumber}
              onVisibilityToggle={() => handleVisibilityToggle("policyNumber")}
            />
            <InputField
              label="Insurance Provider"
              value={form.insuranceProvider}
              onChangeText={(val) => handleChange("insuranceProvider", val)}
              error={errors.insuranceProvider}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.insuranceProvider}
              onVisibilityToggle={() =>
                handleVisibilityToggle("insuranceProvider")
              }
            />
            <InputField
              label="Policy Period (in years)"
              value={form.policyPeriod}
              onChangeText={(val) => handleChange("policyPeriod", val)}
              error={errors.policyPeriod}
              keyboardType="numeric"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.policyPeriod}
              onVisibilityToggle={() => handleVisibilityToggle("policyPeriod")}
            />
            <DatePicker
              label="Effective Date"
              value={form.effectiveDate}
              onChangeDate={(date) =>
                handleChange("effectiveDate", date.toISOString().split("T")[0])
              }
              error={errors.effectiveDate}
              placeholder="Select effective date"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.effectiveDate}
              onVisibilityToggle={() => handleVisibilityToggle("effectiveDate")}
            />
            <DatePicker
              label="Expiration Date"
              value={form.expirationDate}
              onChangeDate={(date) =>
                handleChange("expirationDate", date.toISOString().split("T")[0])
              }
              error={errors.expirationDate}
              placeholder="Select expiration date"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.expirationDate}
              onVisibilityToggle={() =>
                handleVisibilityToggle("expirationDate")
              }
            />
            <InputField
              label="Sum Insured"
              value={form.sumInsured}
              onChangeText={(val) => handleChange("sumInsured", val)}
              error={errors.sumInsured}
              keyboardType="numeric"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.sumInsured}
              onVisibilityToggle={() => handleVisibilityToggle("sumInsured")}
            />
          </View>
        )}

        <CollapsibleHeader
          title="Billing Address Details"
          isCollapsed={isBillingCollapsed}
          onToggle={() => setIsBillingCollapsed(!isBillingCollapsed)}
          showCheckbox
          checkboxText="Same as Home"
          isChecked={form.isSameBillingAddress}
          onCheckboxToggle={() => {
            const newValue = !form.isSameBillingAddress;
            handleChange("isSameBillingAddress", newValue);

            if (newValue) {
              handleChange("billingApartment", form.apartment);
              handleChange("billingStreet", form.street);
              handleChange("billingCity", form.city);
              handleChange("billingState", form.state);
              handleChange("billingZipCode", form.zipCode);
              handleChange("billingCountry", form.country);
            }
          }}
        />
        {!isBillingCollapsed && (
          <View style={{ zIndex: 1, position: "relative" }}>
            <InputField
              label="Billing Apartment/House No./Building"
              value={
                form.isSameBillingAddress
                  ? form.apartment
                  : form.billingApartment
              }
              onChangeText={(val) => handleChange("billingApartment", val)}
              error={errors.billingApartment}
              disabled={form.isSameBillingAddress}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.billingApartment}
              onVisibilityToggle={() =>
                handleVisibilityToggle("billingApartment")
              }
            />
            <InputField
              label="Billing Street"
              value={
                form.isSameBillingAddress ? form.street : form.billingStreet
              }
              onChangeText={(val) => handleChange("billingStreet", val)}
              error={errors.billingStreet}
              disabled={form.isSameBillingAddress}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.billingStreet}
              onVisibilityToggle={() => handleVisibilityToggle("billingStreet")}
            />
            <InputField
              label="Billing City"
              value={form.isSameBillingAddress ? form.city : form.billingCity}
              onChangeText={(val) => handleChange("billingCity", val)}
              error={errors.billingCity}
              disabled={form.isSameBillingAddress}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.billingCity}
              onVisibilityToggle={() => handleVisibilityToggle("billingCity")}
            />
            <InputField
              label="Billing State/Province"
              value={form.isSameBillingAddress ? form.state : form.billingState}
              onChangeText={(val) => handleChange("billingState", val)}
              error={errors.billingState}
              disabled={form.isSameBillingAddress}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.billingState}
              onVisibilityToggle={() => handleVisibilityToggle("billingState")}
            />
            <InputField
              label="Billing ZIP/Postal Code"
              value={
                form.isSameBillingAddress ? form.zipCode : form.billingZipCode
              }
              onChangeText={(val) => handleChange("billingZipCode", val)}
              error={errors.billingZipCode}
              disabled={form.isSameBillingAddress}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.billingZipCode}
              onVisibilityToggle={() =>
                handleVisibilityToggle("billingZipCode")
              }
            />
            <InputField
              label="Billing Country"
              value={
                form.isSameBillingAddress ? form.country : form.billingCountry
              }
              onChangeText={(val) => handleChange("billingCountry", val)}
              error={errors.billingCountry}
              disabled={form.isSameBillingAddress}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.billingCountry}
              onVisibilityToggle={() =>
                handleVisibilityToggle("billingCountry")
              }
            />
          </View>
        )}

        <CollapsibleHeader
          title="Card Details"
          isCollapsed={isCardCollapsed}
          onToggle={() => setIsCardCollapsed(!isCardCollapsed)}
        />
        {!isCardCollapsed && (
          <View style={{ zIndex: 1, position: "relative" }}>
            <InputField
              label="Name on Card"
              value={form.cardName}
              onChangeText={(val) => handleChange("cardName", val)}
              error={errors.cardName}
              placeholder="Enter name as it appears on card"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.cardName}
              onVisibilityToggle={() => handleVisibilityToggle("cardName")}
            />
            <InputField
              label="Card Number"
              value={form.cardNumber}
              onChangeText={(val) => {
                const cleanedValue = val.replace(/\D/g, "");

                let formattedValue = "";
                for (let i = 0; i < cleanedValue.length; i++) {
                  if (i > 0 && i % 4 === 0) {
                    formattedValue += " ";
                  }
                  formattedValue += cleanedValue[i];
                }

                handleChange("cardNumber", formattedValue);

                const validationError = validateCardNumber(formattedValue);
                if (validationError) {
                  setErrors((prev) => ({
                    ...prev,
                    cardNumber: validationError,
                  }));
                } else if (errors.cardNumber) {
                  setErrors((prev) => ({
                    ...prev,
                    cardNumber: "",
                  }));
                }
              }}
              error={errors.cardNumber}
              keyboardType="numeric"
              maxLength={19}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.cardNumber}
              onVisibilityToggle={() => handleVisibilityToggle("cardNumber")}
            />
            <View
              style={{ flexDirection: "row", justifyContent: "space-between" }}
            >
              <View style={{ width: "48%" }}>
                <InputField
                  label="Expiry Date (MM/YY)"
                  value={form.cardExpiry}
                  onChangeText={(val) => {
                    const cleanedValue = val.replace(/\D/g, "");

                    let formattedValue = "";
                    if (cleanedValue.length > 0) {
                      formattedValue = cleanedValue.substring(0, 2);

                      if (cleanedValue.length > 2) {
                        formattedValue += "/" + cleanedValue.substring(2, 4);
                      }
                    }

                    handleChange("cardExpiry", formattedValue);

                    if (formattedValue.length === 5) {
                      const validationError =
                        validateCardExpiry(formattedValue);
                      if (validationError) {
                        setErrors((prev) => ({
                          ...prev,
                          cardExpiry: validationError,
                        }));
                      } else if (errors.cardExpiry) {
                        setErrors((prev) => ({
                          ...prev,
                          cardExpiry: "",
                        }));
                      }
                    }
                  }}
                  error={errors.cardExpiry}
                  placeholder="MM/YY"
                  keyboardType="numeric"
                  maxLength={5}
                  showVisibilityToggle={true}
                  isVisible={fieldVisibility.cardExpiry}
                  onVisibilityToggle={() =>
                    handleVisibilityToggle("cardExpiry")
                  }
                />
              </View>
              <View style={{ width: "48%" }}>
                <InputField
                  label="CVV"
                  value={form.cardCvv}
                  onChangeText={(val) => {
                    const numericValue = val.replace(/\D/g, "");
                    handleChange("cardCvv", numericValue);
                  }}
                  error={errors.cardCvv}
                  keyboardType="numeric"
                  maxLength={4}
                  showVisibilityToggle={true}
                  isVisible={fieldVisibility.cardCvv}
                  onVisibilityToggle={() => handleVisibilityToggle("cardCvv")}
                />
              </View>
            </View>
          </View>
        )}

        <CollapsibleHeader
          title="Bank Account Details"
          isCollapsed={isAccountCollapsed}
          onToggle={() => setIsAccountCollapsed(!isAccountCollapsed)}
        />
        {!isAccountCollapsed && (
          <View style={{ zIndex: 1, position: "relative" }}>
            <InputField
              label="Account Holder Name"
              value={form.accountName}
              onChangeText={(val) => handleChange("accountName", val)}
              error={errors.accountName}
              placeholder="Enter full name as per bank records"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.accountName}
              onVisibilityToggle={() => handleVisibilityToggle("accountName")}
            />
            <InputField
              label="Bank Name"
              value={form.bankName}
              onChangeText={(val) => handleChange("bankName", val)}
              error={errors.bankName}
              showVisibilityToggle={true}
              isVisible={fieldVisibility.bankName}
              onVisibilityToggle={() => handleVisibilityToggle("bankName")}
            />
            <InputField
              label="Bank Account Number"
              value={form.accountNumber}
              onChangeText={(val) => {
                const numericValue = val.replace(/\D/g, "");
                handleChange("accountNumber", numericValue);
              }}
              error={errors.accountNumber}
              keyboardType="numeric"
              placeholder="Enter account number"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.accountNumber}
              onVisibilityToggle={() => handleVisibilityToggle("accountNumber")}
            />
            <InputField
              label="IFSC Code"
              value={form.ifscCode}
              onChangeText={(val) => {
                const formattedValue = val
                  .replace(/[^A-Za-z0-9]/g, "")
                  .toUpperCase();
                handleChange("ifscCode", formattedValue);
              }}
              error={errors.ifscCode}
              autoCapitalize="characters"
              maxLength={11}
              placeholder="e.g., SBIN0000123"
              showVisibilityToggle={true}
              isVisible={fieldVisibility.ifscCode}
              onVisibilityToggle={() => handleVisibilityToggle("ifscCode")}
            />
          </View>
        )}
      </View>
    );
  }
);

PersonalSection.propTypes = {
  form: PropTypes.shape({
    firstName: PropTypes.string,
    middleName: PropTypes.string,
    lastName: PropTypes.string,
    nickname: PropTypes.string,
    email: PropTypes.string,
    phone: PropTypes.string,
    dateOfBirth: PropTypes.string,
    gender: PropTypes.string,
    nationality: PropTypes.string,
    maritalStatus: PropTypes.string,
    spouseName: PropTypes.string,
    // Health Insurance Information
    policyNumber: PropTypes.string,
    insuranceProvider: PropTypes.string,
    policyPeriod: PropTypes.string,
    effectiveDate: PropTypes.string,
    expirationDate: PropTypes.string,
    sumInsured: PropTypes.string,
    // Address fields
    apartment: PropTypes.string,
    street: PropTypes.string,
    city: PropTypes.string,
    state: PropTypes.string,
    zipCode: PropTypes.string,
    country: PropTypes.string,
    // Billing Address fields
    isSameBillingAddress: PropTypes.bool,
    billingApartment: PropTypes.string,
    billingStreet: PropTypes.string,
    billingCity: PropTypes.string,
    billingState: PropTypes.string,
    billingZipCode: PropTypes.string,
    billingCountry: PropTypes.string,
    // Card Details fields
    cardName: PropTypes.string,
    cardNumber: PropTypes.string,
    cardExpiry: PropTypes.string,
    cardCvv: PropTypes.string,
    // Bank Account Details fields
    accountName: PropTypes.string,
    bankName: PropTypes.string,
    accountNumber: PropTypes.string,
    ifscCode: PropTypes.string,
    // Other Address fields
    isSameAddress: PropTypes.bool,
    otherApartment: PropTypes.string,
    otherStreet: PropTypes.string,
    otherCity: PropTypes.string,
    otherState: PropTypes.string,
    otherZipCode: PropTypes.string,
    otherCountry: PropTypes.string,
    // Other Details fields
    secondaryEmail: PropTypes.string,
    secondaryPhone: PropTypes.string,
    personalWebsite: PropTypes.string,
    hobbies: PropTypes.string,
    religion: PropTypes.string,
    preferredContactMethod: PropTypes.string,
    timezone: PropTypes.string,
    // Emergency Contact fields
    emergencyContactName: PropTypes.string,
    emergencyContactRelationship: PropTypes.string,
    emergencyEmail: PropTypes.string,
    emergencyPhone: PropTypes.string,
    emergencyAddress: PropTypes.string,
  }).isRequired,
  handleChange: PropTypes.func.isRequired,
  errors: PropTypes.object,
  setErrors: PropTypes.func,
  phoneInputRef: PropTypes.object,
  onTimezoneDropdownToggle: PropTypes.func,
};

export default PersonalSection;
